#!/bin/bash

# PDF Auto-Cropper Batch Script
# Simple wrapper that calls the Python version

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/crop_downloads.py"

# Check if Python script exists
if [[ ! -f "$PYTHON_SCRIPT" ]]; then
    echo "Error: crop_downloads.py not found in $SCRIPT_DIR"
    exit 1
fi

# Run the Python script
source $SCRIPT_DIR/.venv/bin/activate && python3 "$PYTHON_SCRIPT" "$@"
