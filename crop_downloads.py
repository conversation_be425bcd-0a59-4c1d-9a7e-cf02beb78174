#!/usr/bin/env python3
"""
PDF Auto-Cropper Downloads Script
Processes all PDF files in the Downloads folder and crops them in place
"""

import os
import sys
import glob
import shutil
import tempfile
from pathlib import Path
from datetime import datetime
from crop import crop_pdf

def get_downloads_folder():
    """Get the Downloads folder path"""
    home = Path.home()
    downloads = home / "Downloads"
    return downloads

def find_pdf_files(downloads_dir):
    """Find all PDF files in the Downloads directory"""
    pdf_pattern = str(downloads_dir / "*.pdf")
    pdf_files = glob.glob(pdf_pattern)
    return [Path(f) for f in pdf_files]

def create_backup(pdf_path):
    """Create a backup of the PDF file"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = pdf_path.with_suffix(f".backup.{timestamp}.pdf")
    shutil.copy2(pdf_path, backup_path)
    return backup_path

def crop_pdf_in_place(pdf_path):
    """Crop a PDF file in place"""
    print(f"Processing: {pdf_path.name}")
    
    # Create backup
    try:
        backup_path = create_backup(pdf_path)
        print(f"  Created backup: {backup_path.name}")
    except Exception as e:
        print(f"  ERROR: Failed to create backup - {e}")
        return False
    
    # Create temporary output file
    with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
        temp_path = temp_file.name
    
    try:
        # Crop the PDF using auto-detection
        crop_pdf(
            input_path=str(pdf_path),
            output_path=temp_path,
            left=None,  # Auto-detect
            top=None,   # Auto-detect
            right=None, # Auto-detect
            bottom=None, # Auto-detect
            buffer=0.01,
            footer_height_ratio=0.1
        )
        
        # Replace original with cropped version
        shutil.move(temp_path, pdf_path)
        print(f"  ✅ Successfully cropped: {pdf_path.name}")
        
        # Remove backup on success
        backup_path.unlink()
        return True
        
    except Exception as e:
        print(f"  ❌ ERROR cropping {pdf_path.name}: {e}")
        
        # Clean up temp file if it exists
        if os.path.exists(temp_path):
            os.unlink(temp_path)
        
        # Restore from backup
        try:
            shutil.move(backup_path, pdf_path)
            print(f"  🔄 Restored original from backup")
        except Exception as restore_error:
            print(f"  ⚠️  WARNING: Failed to restore from backup - {restore_error}")
        
        return False

def main():
    print("=" * 60)
    print("PDF Auto-Cropper Downloads Script")
    print("=" * 60)
    print()
    
    # Get Downloads folder
    downloads_dir = get_downloads_folder()
    
    if not downloads_dir.exists():
        print(f"ERROR: Downloads folder not found: {downloads_dir}")
        sys.exit(1)
    
    print(f"Scanning Downloads folder: {downloads_dir}")
    
    # Find PDF files
    pdf_files = find_pdf_files(downloads_dir)
    
    if not pdf_files:
        print("No PDF files found in Downloads folder.")
        return
    
    print(f"\nFound {len(pdf_files)} PDF file(s):")
    for pdf in pdf_files:
        print(f"  - {pdf.name}")
    
    # Ask for confirmation
    # print()
    # response = input("Do you want to proceed with cropping these files? (y/N): ").strip().lower()
    # if response not in ['y', 'yes']:
    #     print("Operation cancelled.")
    #     return
    
    print("\nStarting batch processing...\n")
    
    # Process each PDF
    success_count = 0
    error_count = 0
    
    for pdf_path in pdf_files:
        if crop_pdf_in_place(pdf_path):
            success_count += 1
        else:
            error_count += 1
        print()  # Add spacing between files
    
    # Summary
    print("=" * 60)
    print("Batch Processing Complete")
    print("=" * 60)
    print(f"✅ Successfully processed: {success_count} files")
    if error_count > 0:
        print(f"❌ Failed to process: {error_count} files")
    print()
    
    if error_count == 0:
        print("🎉 All PDF files have been cropped successfully!")
    else:
        print("⚠️  Some files could not be processed. Check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Script interrupted by user.")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
