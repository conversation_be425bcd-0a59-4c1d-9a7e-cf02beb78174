#!/usr/bin/env python3
"""
PDF Auto-Cropper for E-ink Devices
Automatically crops PDF pages to remove margins and creates uniform-sized pages.
"""

import argparse
import sys
import os
import time
from pathlib import Path
import fitz  # PyMuPDF
import numpy as np
from PIL import Image, ImageDraw


def get_content_bbox(page, threshold=0.95, footer_height_ratio=0.1):
    """
    Analyze a PDF page to find the bounding box of actual content.
    Returns the bounding box as (x0, y0, x1, y1) in PDF coordinates.
    Uses special logic to ignore footer content like page numbers.
    """
    # Convert page to image for analysis
    mat = fitz.Matrix(2, 2)  # 2x zoom for better detection
    pix = page.get_pixmap(matrix=mat)
    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
    
    # Convert to grayscale and then to numpy array
    gray = img.convert('L')
    img_array = np.array(gray)
    
    # Find non-white pixels (content)
    # Using threshold to account for slight color variations
    content_mask = img_array < (255 * threshold)
    
    # Find the bounding box of content
    rows = np.any(content_mask, axis=1)
    cols = np.any(content_mask, axis=0)
    
    if not np.any(rows) or not np.any(cols):
        # If no content found, return full page
        return page.rect
    
    y_min, y_max = np.where(rows)[0][[0, -1]]
    x_min, x_max = np.where(cols)[0][[0, -1]]
    
    # Special handling for bottom footer content
    # Check if there's isolated content in the bottom portion that might be footer
    page_height = img_array.shape[0]
    footer_start = int(page_height * (1 - footer_height_ratio))
    
    # Look for content in the footer area
    footer_rows = rows[footer_start:]
    if np.any(footer_rows):
        # Find gaps in content - if there's a significant gap before footer content,
        # treat the footer as separate from main content
        main_content_end = y_max
        
        # Look for the last significant content before potential footer
        # Check for gaps larger than 2% of page height
        gap_threshold = int(page_height * 0.02)
        
        # Scan backwards from y_max to find large gaps
        content_rows = np.where(rows)[0]
        for i in range(len(content_rows) - 1, 0, -1):
            gap_size = content_rows[i] - content_rows[i-1]
            if gap_size > gap_threshold and content_rows[i] > footer_start:
                # Found a significant gap and we're in footer territory
                main_content_end = content_rows[i-1]
                break
        
        # Use the adjusted end point if we found a reasonable break
        if main_content_end < y_max and main_content_end > y_min:
            y_max = main_content_end
    
    # Convert back to PDF coordinates (accounting for 2x zoom)
    page_rect = page.rect
    x0 = page_rect.x0 + (x_min / 2)
    y0 = page_rect.y0 + (y_min / 2)
    x1 = page_rect.x0 + (x_max / 2)
    y1 = page_rect.y0 + (y_max / 2)
    
    return fitz.Rect(x0, y0, x1, y1)


def analyze_pdf_margins(pdf_path, sample_pages=30, buffer=0.01, footer_height_ratio=0.1):
    """
    Analyze a PDF to determine optimal crop margins.
    Returns suggested crop values as (left, top, right, bottom) percentages.
    """
    doc = fitz.open(pdf_path)
    page_count = len(doc)
    
    # Sample pages evenly distributed throughout the document
    if page_count <= sample_pages:
        sample_indices = range(page_count)
    else:
        step = page_count // sample_pages
        sample_indices = range(0, page_count, step)[:sample_pages]
    
    content_boxes = []
    page_sizes = []
    
    print(f"Analyzing {len(sample_indices)} pages to determine optimal crop...")
    
    for i in sample_indices:
        page = doc[i]
        page_rect = page.rect
        content_bbox = get_content_bbox(page, footer_height_ratio=footer_height_ratio)
        
        content_boxes.append(content_bbox)
        page_sizes.append(page_rect)
    
    doc.close()
    
    # Calculate margin percentages for each page
    left_margins = []
    top_margins = []
    right_margins = []
    bottom_margins = []
    
    for content_box, page_rect in zip(content_boxes, page_sizes):
        left_margin = (content_box.x0 - page_rect.x0) / page_rect.width
        top_margin = (content_box.y0 - page_rect.y0) / page_rect.height
        right_margin = (page_rect.x1 - content_box.x1) / page_rect.width
        bottom_margin = (page_rect.y1 - content_box.y1) / page_rect.height
        
        left_margins.append(max(0, left_margin))
        top_margins.append(max(0, top_margin))
        right_margins.append(max(0, right_margin))
        bottom_margins.append(max(0, bottom_margin))
    
    # Use conservative estimates (smaller margins to avoid cutting content)
    # Add a small buffer to prevent content from being too close to edges
    suggested_left = max(0, np.percentile(left_margins, 25) - buffer)
    suggested_top = max(0, np.percentile(top_margins, 25) - buffer)
    suggested_right = max(0, np.percentile(right_margins, 25) - buffer)
    suggested_bottom = max(0, np.percentile(bottom_margins, 25) - buffer)
    
    return suggested_left, suggested_top, suggested_right, suggested_bottom


def crop_pdf(input_path, output_path, left=None, top=None, right=None, bottom=None, buffer=0.01, footer_height_ratio=0.1):
    """
    Crop a PDF with specified margins (as percentages) and create uniform pages.
    """
    doc = fitz.open(input_path)
    
    # If no crop values provided, analyze the PDF to determine them
    if None in [left, top, right, bottom]:
        print("No crop values provided. Analyzing PDF for optimal cropping...")
        auto_left, auto_top, auto_right, auto_bottom = analyze_pdf_margins(input_path, buffer=buffer, footer_height_ratio=footer_height_ratio)
        
        left = left if left is not None else auto_left
        top = top if top is not None else auto_top
        right = right if right is not None else auto_right
        bottom = bottom if bottom is not None else auto_bottom
        
        print(f"Suggested crop margins: L={left:.3f}, T={top:.3f}, R={right:.3f}, B={bottom:.3f}")
    
    # First pass: determine the maximum content dimensions after cropping
    max_width = 0
    max_height = 0
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        page_rect = page.rect
        
        # Calculate crop rectangle
        crop_left = page_rect.x0 + (page_rect.width * left)
        crop_top = page_rect.y0 + (page_rect.height * top)
        crop_right = page_rect.x1 - (page_rect.width * right)
        crop_bottom = page_rect.y1 - (page_rect.height * bottom)
        
        cropped_width = crop_right - crop_left
        cropped_height = crop_bottom - crop_top
        
        max_width = max(max_width, cropped_width)
        max_height = max(max_height, cropped_height)
    
    print(f"Uniform page size will be: {max_width:.1f} x {max_height:.1f} points")
    
    # Second pass: crop and resize pages to uniform size
    output_doc = fitz.open()
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        page_rect = page.rect
        
        # Calculate crop rectangle
        crop_left = page_rect.x0 + (page_rect.width * left)
        crop_top = page_rect.y0 + (page_rect.height * top)
        crop_right = page_rect.x1 - (page_rect.width * right)
        crop_bottom = page_rect.y1 - (page_rect.height * bottom)
        
        crop_rect = fitz.Rect(crop_left, crop_top, crop_right, crop_bottom)
        
        # Create new page with uniform size
        new_page = output_doc.new_page(width=max_width, height=max_height)
        
        # Calculate scaling and positioning to fit cropped content
        cropped_width = crop_rect.width
        cropped_height = crop_rect.height
        
        scale_x = max_width / cropped_width if cropped_width > 0 else 1
        scale_y = max_height / cropped_height if cropped_height > 0 else 1
        scale = min(scale_x, scale_y)  # Use smaller scale to fit entirely
        
        # Center the content
        scaled_width = cropped_width * scale
        scaled_height = cropped_height * scale
        offset_x = (max_width - scaled_width) / 2
        offset_y = (max_height - scaled_height) / 2
        
        # Create transformation matrix
        mat = fitz.Matrix(scale, scale).pretranslate(offset_x - crop_left * scale, 
                                                   offset_y - crop_top * scale)
        
        # Calculate target rectangle for the content
        target_rect = fitz.Rect(offset_x, offset_y, offset_x + scaled_width, offset_y + scaled_height)
        
        # Insert the cropped page content into the target rectangle
        new_page.show_pdf_page(target_rect, doc, page_num, clip=crop_rect)

        # Add progress reporting and memory management
        if page_num % 10 == 0:
            print(f"Processed page {page_num + 1} of {len(doc)}")
            sys.stdout.flush()  # Ensure output is displayed immediately

        # For very large PDFs, periodically save progress to avoid memory issues
        if page_num > 0 and page_num % 100 == 0:
            print(f"Checkpoint: {page_num + 1} pages processed, continuing...")
            sys.stdout.flush()
    
    # Store page counts before closing documents
    original_page_count = len(doc)
    output_page_count = len(output_doc)
    
    # Save the output
    print(f"Saving cropped PDF to {output_path}...")
    print("This may take a while for large files...")

    # Monitor save progress by checking file size
    def monitor_save_progress(file_path, check_interval=2):
        """Monitor file size during save to show progress"""
        if not os.path.exists(file_path):
            return

        last_size = 0
        stable_count = 0

        while True:
            try:
                current_size = os.path.getsize(file_path)
                if current_size > last_size:
                    print(f"File size: {current_size / (1024*1024):.1f} MB")
                    last_size = current_size
                    stable_count = 0
                else:
                    stable_count += 1
                    if stable_count >= 3:  # File size stable for 6 seconds
                        break

                time.sleep(check_interval)
            except (OSError, FileNotFoundError):
                break

    # Use more conservative save options for better performance
    # garbage=1 is much faster than garbage=4, deflate=False avoids compression overhead
    try:
        # Start monitoring in a separate thread would be ideal, but for simplicity, just save
        start_time = time.time()
        output_doc.save(output_path, garbage=1, deflate=False)
        end_time = time.time()
        print(f"PDF saved successfully in {end_time - start_time:.1f} seconds!")

        # Check final file size
        if os.path.exists(output_path):
            final_size = os.path.getsize(output_path)
            print(f"Final file size: {final_size / (1024*1024):.1f} MB")

    except Exception as e:
        print(f"Error during save: {e}")
        print("Trying alternative save method...")
        try:
            # Fallback: save without optimization
            start_time = time.time()
            output_doc.save(output_path)
            end_time = time.time()
            print(f"PDF saved with basic method in {end_time - start_time:.1f} seconds!")
        except Exception as e2:
            print(f"Save failed completely: {e2}")
            raise e2

    output_doc.close()
    doc.close()
    
    print(f"Successfully created cropped PDF: {output_path}")
    print(f"Original pages: {original_page_count}, Output pages: {output_page_count}")


def main():
    parser = argparse.ArgumentParser(
        description="Auto-crop PDF pages for better e-ink device viewing",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python pdf_cropper.py document.pdf
  python pdf_cropper.py document.pdf --left 0.1 --right 0.1 --top 0.05 --bottom 0.05
  python pdf_cropper.py document.pdf -l 0.15 -r 0.1 -t 0.08 -b 0.12
  python pdf_cropper.py document.pdf --buffer 0.02
  python pdf_cropper.py document.pdf --footer-height 0.15

Crop values are percentages (0.0 to 1.0) of the page dimensions to remove.
For example, --left 0.1 removes 10% from the left margin.
Buffer adds extra space around auto-detected content (default 1%).
Footer-height controls how much of the bottom area to check for footer content.
        """
    )
    
    parser.add_argument("input_pdf", help="Input PDF file path")
    parser.add_argument("-l", "--left", type=float, 
                       help="Left margin to crop (0.0-1.0, default: auto-detect)")
    parser.add_argument("-r", "--right", type=float,
                       help="Right margin to crop (0.0-1.0, default: auto-detect)")
    parser.add_argument("-t", "--top", type=float,
                       help="Top margin to crop (0.0-1.0, default: auto-detect)")
    parser.add_argument("-b", "--bottom", type=float,
                       help="Bottom margin to crop (0.0-1.0, default: auto-detect)")
    parser.add_argument("--footer-height", type=float, default=0.1,
                       help="Height ratio to check for footer content (0.05-0.2, default: 0.1)")
    parser.add_argument("--buffer", type=float, default=0.01,
                       help="Buffer space around auto-detected content (0.0-0.1, default: 0.01)")
    parser.add_argument("-o", "--output", 
                       help="Output PDF path (default: input_filename-cropped.pdf)")
    
    args = parser.parse_args()
    
    # Validate input file
    input_path = Path(args.input_pdf)
    if not input_path.exists():
        print(f"Error: Input file '{input_path}' not found.")
        sys.exit(1)
    
    if not input_path.suffix.lower() == '.pdf':
        print(f"Error: Input file must be a PDF.")
        sys.exit(1)
    
    # Determine output path
    if args.output:
        output_path = Path(args.output)
    else:
        output_path = input_path.parent / f"{input_path.stem}-cropped.pdf"
    
    # Validate crop parameters
    crop_params = [args.left, args.right, args.top, args.bottom]
    for param in crop_params:
        if param is not None and (param < 0 or param >= 1):
            print("Error: Crop values must be between 0.0 and 1.0")
            sys.exit(1)
    
    # Validate buffer parameter
    if args.buffer < 0 or args.buffer > 0.1:
        print("Error: Buffer value must be between 0.0 and 0.1")
        sys.exit(1)
    
    # Validate footer height parameter
    if args.footer_height < 0.05 or args.footer_height > 0.2:
        print("Error: Footer height must be between 0.05 and 0.2")
        sys.exit(1)
    
    try:
        print(f"Processing: {input_path}")
        print(f"Output will be saved as: {output_path}")
        
        crop_pdf(str(input_path), str(output_path), 
                args.left, args.top, args.right, args.bottom, args.buffer, args.footer_height)
        
        print("Done! Your PDF has been cropped and optimized for e-ink viewing.")
        
    except Exception as e:
        print(f"Error processing PDF: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
